/* CSS Variables for Design System */
:root {
  /* Colors */
  --primary-blue: #1e3a8a;
  --primary-blue-light: #3b82f6;
  --primary-blue-dark: #1e40af;
  --secondary-silver: #64748b;
  --secondary-silver-light: #94a3b8;
  --accent-red: #dc2626;
  --accent-red-light: #ef4444;

  /* Neutrals */
  --white: #ffffff;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  width: 100%;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-primary);
  line-height: 1.6;
  color: var(--gray-800);
  background-color: var(--gray-50);
  overflow-x: hidden;
  margin: 0;
  padding: 0;
  width: 100%;
  display: flex;
  justify-content: center;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 100vw;
  margin: 0;
  position: relative;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-light) 100%);
  color: var(--white);
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-blue-dark) 0%, var(--primary-blue) 100%);
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.btn-secondary {
  background: var(--white);
  color: var(--gray-700);
  border: 2px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  background: var(--gray-50);
  border-color: var(--gray-300);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-outline {
  background: transparent;
  color: var(--primary-blue);
  border: 2px solid var(--primary-blue);
}

.btn-outline:hover {
  background: var(--primary-blue);
  color: var(--white);
  transform: translateY(-1px);
}

.btn-icon {
  font-size: 0.75rem;
}

/* Header Styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--gray-200);
  z-index: 1000;
  transition: all var(--transition-normal);
  width: 100%;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
  width: 100%;
  box-sizing: border-box;
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-weight: var(--font-weight-bold);
  font-size: 1.5rem;
  color: var(--primary-blue);
  text-decoration: none;
}

.logo-icon {
  font-size: 2rem;
  color: var(--accent-red);
}

.nav-list {
  display: flex;
  list-style: none;
  gap: var(--spacing-xl);
}

.nav-link {
  color: var(--gray-700);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: color var(--transition-fast);
  position: relative;
}

.nav-link:hover {
  color: var(--primary-blue);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-blue);
  transition: width var(--transition-fast);
}

.nav-link:hover::after {
  width: 100%;
}

.menu-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--gray-700);
  cursor: pointer;
}

/* Hero Styles */
.hero {
  margin-top: 70px;
  background: linear-gradient(135deg,
    var(--gray-50) 0%,
    var(--gray-100) 25%,
    var(--white) 50%,
    var(--gray-100) 75%,
    var(--gray-200) 100%
  );
  color: var(--gray-900);
  overflow: hidden;
  position: relative;
  width: 100%;
  display: flex;
  justify-content: center;
  border-bottom: 1px solid var(--gray-200);
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(30, 58, 138, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(220, 38, 38, 0.03) 0%, transparent 50%),
    linear-gradient(135deg, rgba(100, 116, 139, 0.02) 0%, transparent 100%);
  background-size: 100% 100%, 100% 100%, 100% 100%;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-3xl) var(--spacing-lg);
  position: relative;
  z-index: 1;
  width: 100%;
  box-sizing: border-box;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
  align-items: center;
  min-height: 500px;
  width: 100%;
}

.hero-title {
  font-size: 3.2rem;
  font-weight: var(--font-weight-bold);
  line-height: 1.2;
  margin-bottom: var(--spacing-lg);
  color: var(--gray-900);
  letter-spacing: -0.02em;
}

.hero-highlight {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--accent-red) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: inline-block;
}

.hero-description {
  font-size: 1.125rem;
  color: var(--gray-600);
  margin-bottom: var(--spacing-xl);
  line-height: 1.7;
  font-weight: var(--font-weight-normal);
  max-width: 90%;
}

.hero-buttons {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
  margin-top: var(--spacing-sm);
}

.hero-buttons .btn {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: 1rem;
  font-weight: var(--font-weight-semibold);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
}

.hero-buttons .btn-primary {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
  box-shadow: 0 4px 14px 0 rgba(30, 58, 138, 0.25);
}

.hero-buttons .btn-primary:hover {
  box-shadow: 0 6px 20px 0 rgba(30, 58, 138, 0.35);
  transform: translateY(-2px);
}

.hero-buttons .btn-secondary {
  background: var(--white);
  color: var(--gray-700);
  border: 2px solid var(--gray-300);
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
}

.hero-buttons .btn-secondary:hover {
  border-color: var(--primary-blue);
  color: var(--primary-blue);
  box-shadow: 0 4px 12px 0 rgba(30, 58, 138, 0.15);
  transform: translateY(-1px);
}

/* Hero Carousel Styles */
.hero-carousel {
  position: relative;
}

.carousel-container {
  position: relative;
  border-radius: var(--radius-2xl);
  overflow: hidden;
  box-shadow: 0 20px 40px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid var(--gray-200);
  background: var(--white);
}

.carousel-slide {
  position: relative;
  height: 400px;
}

.car-showcase {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: var(--radius-2xl);
  overflow: hidden;
}

.showcase-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-slow);
}

.car-showcase:hover .showcase-image {
  transform: scale(1.05);
}

.showcase-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(15, 23, 42, 0.9));
  padding: var(--spacing-xl);
  color: var(--white);
  backdrop-filter: blur(8px);
}

.showcase-name {
  font-size: 1.5rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-sm);
  color: var(--white);
}

.showcase-price {
  font-size: 1.25rem;
  color: var(--primary-blue-light);
  margin-bottom: var(--spacing-sm);
  font-weight: var(--font-weight-semibold);
}

.showcase-badge {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  background: linear-gradient(135deg, var(--accent-red) 0%, var(--accent-red-light) 100%);
  color: var(--white);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px 0 rgba(220, 38, 38, 0.3);
}

.carousel-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid var(--gray-200);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  z-index: 2;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
  color: var(--gray-700);
  backdrop-filter: blur(8px);
}

.carousel-btn:hover {
  background: var(--white);
  transform: translateY(-50%) scale(1.05);
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.2);
  color: var(--primary-blue);
  border-color: var(--primary-blue);
}

.carousel-btn-prev {
  left: var(--spacing-lg);
}

.carousel-btn-next {
  right: var(--spacing-lg);
}

.carousel-indicators {
  display: flex;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
  padding: var(--spacing-md);
}

.indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: 2px solid var(--gray-300);
  background: var(--white);
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
}

.indicator:hover {
  border-color: var(--primary-blue);
  transform: scale(1.1);
}

.indicator.active {
  background: var(--primary-blue);
  border-color: var(--primary-blue);
  transform: scale(1.2);
  box-shadow: 0 2px 8px 0 rgba(30, 58, 138, 0.3);
}

/* Search Filter Styles */
.search-filter {
  background: var(--white);
  padding: var(--spacing-3xl) 0;
  border-bottom: 1px solid var(--gray-200);
  width: 100%;
  display: flex;
  justify-content: center;
}

.search-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  width: 100%;
  box-sizing: border-box;
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
}

.search-title {
  font-size: 2rem;
  font-weight: var(--font-weight-bold);
  color: var(--gray-900);
}

.filter-toggle {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: var(--gray-100);
  border: none;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--gray-700);
  font-weight: var(--font-weight-medium);
}

.filter-toggle:hover {
  background: var(--gray-200);
  color: var(--primary-blue);
}

.quick-search {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) auto;
  gap: var(--spacing-lg);
  align-items: end;
  margin-bottom: var(--spacing-xl);
  justify-items: center;
  width: 100%;
}

.search-group {
  display: flex;
  flex-direction: column;
}

.search-select,
.search-input {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-md);
  font-size: 1rem;
  transition: all var(--transition-fast);
  background: var(--white);
}

.search-select:focus,
.search-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-btn {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-light) 100%);
  color: var(--white);
  border: none;
  padding: var(--spacing-sm) var(--spacing-xl);
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  box-shadow: var(--shadow-md);
}

.search-btn:hover {
  background: linear-gradient(135deg, var(--primary-blue-dark) 0%, var(--primary-blue) 100%);
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.advanced-filters {
  background: var(--gray-50);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  margin-top: var(--spacing-lg);
  max-height: 0;
  overflow: hidden;
  transition: all var(--transition-normal);
  opacity: 0;
}

.filters-open {
  max-height: 500px;
  opacity: 1;
}

.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.filters-header h3 {
  font-size: 1.25rem;
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
}

.close-filters {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: var(--gray-500);
  cursor: pointer;
  transition: color var(--transition-fast);
}

.close-filters:hover {
  color: var(--gray-700);
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.filter-group label {
  font-weight: var(--font-weight-medium);
  color: var(--gray-700);
  font-size: 0.875rem;
}

.filter-select,
.filter-input {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-md);
  font-size: 1rem;
  transition: all var(--transition-fast);
  background: var(--white);
}

.filter-select:focus,
.filter-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filters-actions {
  display: flex;
  gap: var(--spacing-lg);
  justify-content: flex-end;
}

/* Car Grid Styles */
.car-grid-section {
  padding: var(--spacing-3xl) 0;
  background: var(--gray-50);
  width: 100%;
  display: flex;
  justify-content: center;
}

.grid-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  width: 100%;
  box-sizing: border-box;
}

.grid-header {
  text-align: center;
  margin-bottom: var(--spacing-3xl);
}

.grid-title {
  font-size: 2.5rem;
  font-weight: var(--font-weight-bold);
  color: var(--gray-900);
  margin-bottom: var(--spacing-md);
}

.grid-subtitle {
  font-size: 1.125rem;
  color: var(--gray-600);
  margin-bottom: var(--spacing-lg);
}

.results-count {
  display: inline-block;
  padding: var(--spacing-sm) var(--spacing-lg);
  background: var(--primary-blue);
  color: var(--white);
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
  font-size: 0.875rem;
}

.car-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-xl);
  position: relative;
  width: 100%;
  justify-items: center;
  place-items: center;
  margin: 0 auto;
}

.grid-item {
  animation: fadeInUp 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
}

.grid-item.elevated {
  transform: translateY(-20px);
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.no-results {
  text-align: center;
  padding: var(--spacing-3xl);
}

.no-results-content h3 {
  font-size: 1.5rem;
  color: var(--gray-700);
  margin-bottom: var(--spacing-md);
}

.no-results-content p {
  color: var(--gray-500);
}

/* Car Card Styles */
.car-card {
  background: var(--white);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 400px;
}

.car-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
}

.card-image-container {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-slow);
}

.car-card:hover .card-image {
  transform: scale(1.1);
}

.card-overlay {
  position: absolute;
  top: var(--spacing-md);
  left: var(--spacing-md);
  right: var(--spacing-md);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.like-btn {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--gray-600);
}

.like-btn:hover,
.like-btn.liked {
  background: var(--accent-red);
  color: var(--white);
  transform: scale(1.1);
}

.condition-badge {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.condition-badge.new {
  background: var(--primary-blue);
  color: var(--white);
}

.condition-badge.used {
  background: var(--secondary-silver);
  color: var(--white);
}

.card-hover-actions {
  position: absolute;
  bottom: var(--spacing-md);
  left: var(--spacing-md);
  right: var(--spacing-md);
  opacity: 0;
  transform: translateY(20px);
  transition: all var(--transition-normal);
}

.car-card:hover .card-hover-actions {
  opacity: 1;
  transform: translateY(0);
}

.action-btn {
  background: rgba(255, 255, 255, 0.95);
  border: none;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--gray-700);
  backdrop-filter: blur(10px);
}

.action-btn:hover {
  background: var(--white);
  color: var(--primary-blue);
  transform: translateY(-2px);
}

.card-content {
  padding: var(--spacing-xl);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-header {
  margin-bottom: var(--spacing-lg);
}

.car-name {
  font-size: 1.25rem;
  font-weight: var(--font-weight-bold);
  color: var(--gray-900);
  margin-bottom: var(--spacing-sm);
}

.car-price {
  font-size: 1.5rem;
  font-weight: var(--font-weight-bold);
  color: var(--primary-blue);
}

.car-details {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md) 0;
  border-top: 1px solid var(--gray-200);
  border-bottom: 1px solid var(--gray-200);
}

.detail-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.875rem;
  color: var(--gray-600);
}

.detail-icon {
  color: var(--primary-blue);
  font-size: 1rem;
}

.card-actions {
  display: flex;
  gap: var(--spacing-md);
  margin-top: auto;
}

.card-actions .btn {
  flex: 1;
  justify-content: center;
}

/* Footer Styles */
.footer {
  background: var(--gray-900);
  color: var(--white);
  margin-top: auto;
  width: 100%;
  display: flex;
  justify-content: center;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-3xl) var(--spacing-lg) 0;
  width: 100%;
  box-sizing: border-box;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr;
  gap: var(--spacing-3xl);
  margin-bottom: var(--spacing-3xl);
}

.footer-section h3 {
  margin-bottom: var(--spacing-lg);
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-weight: var(--font-weight-bold);
  font-size: 1.5rem;
  margin-bottom: var(--spacing-lg);
}

.footer-logo-icon {
  font-size: 2rem;
  color: var(--accent-red);
}

.footer-description {
  color: var(--gray-300);
  line-height: 1.7;
  margin-bottom: var(--spacing-xl);
}

.social-links {
  display: flex;
  gap: var(--spacing-md);
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--gray-800);
  color: var(--gray-300);
  border-radius: 50%;
  text-decoration: none;
  transition: all var(--transition-fast);
}

.social-link:hover {
  background: var(--primary-blue);
  color: var(--white);
  transform: translateY(-2px);
}

.footer-title {
  font-size: 1.125rem;
  font-weight: var(--font-weight-semibold);
  color: var(--white);
  margin-bottom: var(--spacing-lg);
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-bottom: var(--spacing-sm);
}

.footer-links a {
  color: var(--gray-300);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.footer-links a:hover {
  color: var(--primary-blue-light);
}

.contact-info {
  margin-bottom: var(--spacing-xl);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  color: var(--gray-300);
  transition: all var(--transition-fast);
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
}

.contact-item:hover {
  color: var(--primary-blue-light);
  background: rgba(59, 130, 246, 0.1);
  transform: translateX(4px);
}

.contact-icon {
  color: var(--primary-blue-light);
  font-size: 1rem;
}

.business-hours h4 {
  color: var(--white);
  margin-bottom: var(--spacing-sm);
  font-weight: var(--font-weight-medium);
}

.business-hours p {
  color: var(--gray-300);
  font-size: 0.875rem;
  margin-bottom: var(--spacing-xs);
}

.footer-bottom {
  border-top: 1px solid var(--gray-800);
  padding: var(--spacing-xl) 0;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: var(--gray-400);
  font-size: 0.875rem;
}

.footer-bottom-links {
  display: flex;
  gap: var(--spacing-lg);
}

.footer-bottom-links a {
  color: var(--gray-400);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.footer-bottom-links a:hover {
  color: var(--primary-blue-light);
}

/* Responsive Design */

/* Large Desktop (1440px+) */
@media (min-width: 1440px) {
  .header-container,
  .hero-container,
  .search-container,
  .grid-container,
  .footer-container {
    max-width: 1400px;
  }

  .hero-title {
    font-size: 4rem;
  }

  .car-grid {
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  }
}

/* Desktop (1200px - 1439px) */
@media (max-width: 1439px) and (min-width: 1200px) {
  .car-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Medium Desktop/Large Tablet (1024px - 1199px) */
@media (max-width: 1199px) and (min-width: 1024px) {
  .hero-content {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
  }

  .hero-title {
    font-size: 3.2rem;
  }

  .car-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-xl);
  }

  .footer-content {
    grid-template-columns: 2fr 1fr 1fr;
    gap: var(--spacing-2xl);
  }

  .quick-search {
    grid-template-columns: repeat(2, 1fr) auto;
  }
}

/* Tablet Portrait (768px - 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
  .header-container {
    padding: 0 var(--spacing-lg);
  }

  .hero-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
    text-align: center;
  }

  .hero-title {
    font-size: 3rem;
  }

  .hero-carousel {
    max-width: 500px;
    margin: 0 auto;
  }

  .search-header {
    flex-direction: column;
    gap: var(--spacing-lg);
    align-items: center;
    text-align: center;
  }

  .quick-search {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }

  .search-btn {
    grid-column: 1 / -1;
    justify-self: center;
    max-width: 200px;
  }

  .car-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
  }

  .grid-item.elevated {
    transform: translateY(-10px);
  }

  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
  }

  .filters-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Mobile Landscape/Large Mobile (640px - 767px) */
@media (max-width: 767px) and (min-width: 640px) {
  .header-container {
    padding: 0 var(--spacing-md);
    height: 60px;
  }

  .hero {
    margin-top: 60px;
  }

  .logo {
    font-size: 1.25rem;
  }

  .logo-icon {
    font-size: 1.5rem;
  }

  .nav {
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--gray-200);
    transform: translateY(-100%);
    transition: transform var(--transition-normal);
    z-index: 999;
    box-shadow: var(--shadow-lg);
  }

  .nav-open {
    transform: translateY(0);
  }

  .nav-list {
    flex-direction: column;
    padding: var(--spacing-lg);
    gap: var(--spacing-md);
  }

  .nav-link {
    padding: var(--spacing-sm) 0;
    font-size: 1.1rem;
  }

  .menu-toggle {
    display: block;
  }

  .hero-container {
    padding: var(--spacing-2xl) var(--spacing-md);
  }

  .hero-title {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  .hero-description {
    font-size: 1rem;
  }

  .hero-buttons {
    justify-content: center;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
  }

  .hero-buttons .btn {
    width: 100%;
    max-width: 280px;
  }

  .quick-search {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .car-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .grid-item.elevated {
    transform: translateY(0);
  }

  .carousel-slide {
    height: 300px;
  }

  .carousel-btn {
    width: 40px;
    height: 40px;
  }

  .carousel-btn-prev {
    left: var(--spacing-sm);
  }

  .carousel-btn-next {
    right: var(--spacing-sm);
  }
}

/* Mobile Portrait (480px - 639px) */
@media (max-width: 639px) and (min-width: 480px) {
  .header-container {
    padding: 0 var(--spacing-md);
    height: 60px;
  }

  .hero {
    margin-top: 60px;
  }

  .logo {
    font-size: 1.1rem;
  }

  .logo-icon {
    font-size: 1.4rem;
  }

  .nav {
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--gray-200);
    transform: translateY(-100%);
    transition: transform var(--transition-normal);
    z-index: 999;
    box-shadow: var(--shadow-lg);
  }

  .nav-open {
    transform: translateY(0);
  }

  .nav-list {
    flex-direction: column;
    padding: var(--spacing-lg) var(--spacing-md);
    gap: var(--spacing-md);
  }

  .nav-link {
    padding: var(--spacing-sm) 0;
    font-size: 1rem;
    text-align: center;
  }

  .menu-toggle {
    display: block;
  }

  .hero-container {
    padding: var(--spacing-xl) var(--spacing-md);
  }

  .hero-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
    text-align: center;
  }

  .hero-title {
    font-size: 2.2rem;
    line-height: 1.2;
  }

  .hero-description {
    font-size: 0.95rem;
  }

  .hero-buttons {
    justify-content: center;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
  }

  .hero-buttons .btn {
    width: 100%;
    max-width: 250px;
    padding: var(--spacing-md) var(--spacing-lg);
  }

  .search-container {
    padding: 0 var(--spacing-md);
  }

  .search-title {
    font-size: 1.75rem;
  }

  .grid-title {
    font-size: 2rem;
  }

  .grid-container {
    padding: 0 var(--spacing-md);
  }

  .car-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .carousel-slide {
    height: 280px;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
    text-align: center;
  }

  .footer-bottom-content {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }

  .filters-grid {
    grid-template-columns: 1fr;
  }

  .card-actions {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .filters-actions {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
}

/* Small Mobile (320px - 479px) */
@media (max-width: 479px) {
  .header-container {
    padding: 0 var(--spacing-sm);
    height: 55px;
  }

  .hero {
    margin-top: 55px;
  }

  .logo {
    font-size: 1rem;
  }

  .logo-icon {
    font-size: 1.2rem;
  }

  .nav {
    position: fixed;
    top: 55px;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--gray-200);
    transform: translateY(-100%);
    transition: transform var(--transition-normal);
    z-index: 999;
    box-shadow: var(--shadow-lg);
  }

  .nav-open {
    transform: translateY(0);
  }

  .nav-list {
    flex-direction: column;
    padding: var(--spacing-md);
    gap: var(--spacing-sm);
  }

  .nav-link {
    padding: var(--spacing-sm) 0;
    font-size: 0.95rem;
    text-align: center;
  }

  .menu-toggle {
    display: block;
    font-size: 1.2rem;
  }

  .hero-container {
    padding: var(--spacing-lg) var(--spacing-sm);
  }

  .hero-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
    text-align: center;
  }

  .hero-title {
    font-size: 1.8rem;
    line-height: 1.2;
  }

  .hero-description {
    font-size: 0.9rem;
    line-height: 1.6;
  }

  .hero-buttons {
    justify-content: center;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
  }

  .hero-buttons .btn {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.85rem;
  }

  .search-container {
    padding: 0 var(--spacing-sm);
  }

  .search-filter {
    padding: var(--spacing-xl) 0;
  }

  .search-title {
    font-size: 1.5rem;
  }

  .filter-toggle {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: 0.8rem;
  }

  .grid-title {
    font-size: 1.75rem;
  }

  .grid-subtitle {
    font-size: 1rem;
  }

  .grid-container {
    padding: 0 var(--spacing-sm);
  }

  .car-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .car-card {
    border-radius: var(--radius-lg);
  }

  .card-image-container {
    height: 200px;
  }

  .card-content {
    padding: var(--spacing-lg);
  }

  .car-name {
    font-size: 1.1rem;
  }

  .car-price {
    font-size: 1.3rem;
  }

  .carousel-slide {
    height: 250px;
  }

  .carousel-btn {
    width: 35px;
    height: 35px;
    font-size: 0.8rem;
  }

  .carousel-btn-prev {
    left: var(--spacing-xs);
  }

  .carousel-btn-next {
    right: var(--spacing-xs);
  }

  .showcase-name {
    font-size: 1.2rem;
  }

  .showcase-price {
    font-size: 1.1rem;
  }

  .footer-container {
    padding: var(--spacing-xl) var(--spacing-sm) 0;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
    text-align: center;
  }

  .footer-bottom {
    padding: var(--spacing-md) 0;
  }

  .footer-bottom-content {
    flex-direction: column;
    gap: var(--spacing-sm);
    text-align: center;
    font-size: 0.8rem;
  }

  .footer-bottom-links {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .social-links {
    justify-content: center;
  }

  .social-link {
    width: 35px;
    height: 35px;
  }

  .filters-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .card-actions {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .card-actions .btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.8rem;
  }

  .filters-actions {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .advanced-filters {
    padding: var(--spacing-md);
  }

  .detail-item {
    font-size: 0.8rem;
  }

  .detail-icon {
    font-size: 0.9rem;
  }
}

/* Extra Small Mobile (below 320px) */
@media (max-width: 319px) {
  .hero-title {
    font-size: 1.6rem;
  }

  .grid-title {
    font-size: 1.5rem;
  }

  .search-title {
    font-size: 1.3rem;
  }

  .carousel-slide {
    height: 220px;
  }

  .card-image-container {
    height: 180px;
  }
}

/* Touch and Accessibility Improvements */
@media (hover: none) and (pointer: coarse) {
  /* Touch devices */
  .btn {
    min-height: 44px;
    min-width: 44px;
  }

  .nav-link {
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .carousel-btn {
    min-width: 44px;
    min-height: 44px;
  }

  .like-btn {
    min-width: 44px;
    min-height: 44px;
  }

  .menu-toggle {
    min-width: 44px;
    min-height: 44px;
  }

  .filter-toggle {
    min-height: 44px;
  }

  .social-link {
    min-width: 44px;
    min-height: 44px;
  }

  /* Remove hover effects on touch devices */
  .car-card:hover {
    transform: none;
    box-shadow: var(--shadow-lg);
  }

  .car-card:hover .card-image {
    transform: none;
  }

  .car-showcase:hover .showcase-image {
    transform: none;
  }

  .btn-primary:hover {
    transform: none;
  }

  .btn-secondary:hover {
    transform: none;
  }

  .btn-outline:hover {
    transform: none;
  }

  .action-btn:hover {
    transform: none;
  }

  .social-link:hover {
    transform: none;
  }

  .carousel-btn:hover {
    transform: translateY(-50%);
  }

  .like-btn:hover {
    transform: none;
  }
}

/* High DPI/Retina Display Optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .logo-icon,
  .footer-logo-icon {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Landscape orientation on mobile */
@media (max-height: 500px) and (orientation: landscape) {
  .hero-container {
    padding: var(--spacing-lg) var(--spacing-md);
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-description {
    font-size: 0.9rem;
  }

  .hero-buttons {
    flex-direction: row;
    gap: var(--spacing-md);
  }

  .hero-buttons .btn {
    width: auto;
    max-width: none;
  }

  .carousel-slide {
    height: 250px;
  }
}

/* Print styles */
@media print {
  .header,
  .hero,
  .search-filter,
  .footer {
    display: none;
  }

  .car-grid-section {
    padding: 0;
  }

  .car-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid var(--gray-300);
  }

  .card-actions {
    display: none;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .grid-item {
    animation: none;
    opacity: 1;
    transform: none;
  }

  .carousel-btn:hover {
    transform: translateY(-50%);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --gray-50: #0f172a;
    --gray-100: #1e293b;
    --gray-200: #334155;
    --gray-300: #475569;
    --gray-400: #64748b;
    --gray-500: #94a3b8;
    --gray-600: #cbd5e1;
    --gray-700: #e2e8f0;
    --gray-800: #f1f5f9;
    --gray-900: #f8fafc;
    --white: #0f172a;
  }

  .header {
    background: rgba(15, 23, 42, 0.95);
    border-bottom-color: var(--gray-200);
  }

  .nav {
    background: rgba(15, 23, 42, 0.98);
  }

  .hero::before {
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="%23000000" fill-opacity="0.02" points="0,1000 1000,0 1000,1000"/></svg>');
  }
}
