import { useState, useEffect } from 'react'
import { FaPlay, FaChevronDown } from 'react-icons/fa'

const VideoHero = () => {
  const [isVideoLoaded, setIsVideoLoaded] = useState(false)
  const [currentTextIndex, setCurrentTextIndex] = useState(0)

  const heroTexts = [
    {
      title: "Performance Unleashed",
      subtitle: "Pure Automotive Excellence",
      description: "Experience the thrill of precision engineering and uncompromising performance"
    },
    {
      title: "Luxury in Motion",
      subtitle: "Crafted for Perfection",
      description: "Where cutting-edge technology meets timeless elegance on every journey"
    },
    {
      title: "Drive Your Dreams",
      subtitle: "Premium Collection",
      description: "Discover extraordinary vehicles that redefine what it means to drive in style"
    }
  ]

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTextIndex((prev) => (prev + 1) % heroTexts.length)
    }, 4000)
    return () => clearInterval(interval)
  }, [])

  const scrollToInventory = () => {
    const inventorySection = document.querySelector('.search-filter')
    if (inventorySection) {
      inventorySection.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <section className="video-hero">
      <div className="video-container">
        <video
          className="hero-video"
          autoPlay
          muted
          loop
          playsInline
          onLoadedData={() => setIsVideoLoaded(true)}
          onError={() => setIsVideoLoaded(true)}
        >
          <source src="https://player.vimeo.com/external/434045526.sd.mp4?s=c27eecc69a27dbc4ff2b87d38afc35f1a9e7c02d&profile_id=139" type="video/mp4" />
          <source src="https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_2mb.mp4" type="video/mp4" />
          <source src="https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4" type="video/mp4" />
        </video>

        {/* Fallback background when video fails to load */}
        <div className="hero-fallback-bg"></div>

        <div className="video-overlay"></div>

        <div className="hero-content-wrapper">
          <div className="hero-content-container">
            <div className={`hero-text-content ${isVideoLoaded ? 'loaded' : ''}`}>
              <div className="hero-badge">
                <span className="badge-text">Premium Collection</span>
                <div className="badge-glow"></div>
              </div>

              <div className="hero-text-slider">
                {heroTexts.map((text, index) => (
                  <div
                    key={index}
                    className={`hero-text-slide ${index === currentTextIndex ? 'active' : ''}`}
                  >
                    <h1 className="hero-title">
                      <span className="title-main">{text.title}</span>
                      <span className="title-accent">{text.subtitle}</span>
                    </h1>
                    <p className="hero-description">{text.description}</p>
                  </div>
                ))}
              </div>

              <div className="hero-actions">
                <button className="btn-premium primary" onClick={scrollToInventory}>
                  <span>Explore Collection</span>
                  <div className="btn-glow"></div>
                </button>
                <button className="btn-premium secondary">
                  <FaPlay className="play-icon" />
                  <span>Watch Story</span>
                </button>
              </div>

              <div className="hero-stats">
                <div className="stat-item">
                  <span className="stat-number">500+</span>
                  <span className="stat-label">Premium Vehicles</span>
                </div>
                <div className="stat-divider"></div>
                <div className="stat-item">
                  <span className="stat-number">15+</span>
                  <span className="stat-label">Luxury Brands</span>
                </div>
                <div className="stat-divider"></div>
                <div className="stat-item">
                  <span className="stat-number">VIP</span>
                  <span className="stat-label">Experience</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="scroll-indicator" onClick={scrollToInventory}>
          <div className="scroll-text">Discover More</div>
          <FaChevronDown className="scroll-arrow" />
        </div>
      </div>
    </section>
  )
}

export default VideoHero
