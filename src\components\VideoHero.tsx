import { useState, useEffect } from 'react'
import { FaPlay, FaChevronDown } from 'react-icons/fa'

const VideoHero = () => {
  const [isVideoLoaded, setIsVideoLoaded] = useState(false)
  const [currentTextIndex, setCurrentTextIndex] = useState(0)

  const heroTexts = [
    {
      title: "Racing Through Europe",
      subtitle: "Mountain Road Mastery",
      description: "Experience the ultimate thrill of precision driving through Europe's most scenic routes"
    },
    {
      title: "Alpine Adventures",
      subtitle: "Performance Perfected",
      description: "Where cutting-edge engineering conquers the most challenging mountain passes"
    },
    {
      title: "European Elegance",
      subtitle: "Racing Heritage",
      description: "Discover vehicles born from racing DNA and European automotive excellence"
    }
  ]

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTextIndex((prev) => (prev + 1) % heroTexts.length)
    }, 4000)
    return () => clearInterval(interval)
  }, [])

  const scrollToInventory = () => {
    const inventorySection = document.querySelector('.search-filter')
    if (inventorySection) {
      inventorySection.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <section className="video-hero">
      <div className="video-container">
        <video
          className="hero-video"
          autoPlay
          muted
          loop
          playsInline
          onLoadedData={() => setIsVideoLoaded(true)}
          onError={() => setIsVideoLoaded(true)}
        >
          <source src="https://videos.pexels.com/video-files/1409899/1409899-uhd_2560_1440_30fps.mp4" type="video/mp4" />
          <source src="https://videos.pexels.com/video-files/2103099/2103099-uhd_2560_1440_30fps.mp4" type="video/mp4" />
          <source src="https://videos.pexels.com/video-files/3571264/3571264-uhd_2560_1440_30fps.mp4" type="video/mp4" />
          <source src="https://videos.pexels.com/video-files/855564/855564-hd_1920_1080_30fps.mp4" type="video/mp4" />
          <source src="https://videos.pexels.com/video-files/1448735/1448735-hd_1920_1080_30fps.mp4" type="video/mp4" />
        </video>

        {/* Fallback background when video fails to load */}
        <div className="hero-fallback-bg"></div>

        <div className="video-overlay"></div>

        <div className="hero-content-wrapper">
          <div className="hero-content-container">
            <div className={`hero-text-content ${isVideoLoaded ? 'loaded' : ''}`}>
              <div className="hero-badge">
                <span className="badge-text">Premium Collection</span>
                <div className="badge-glow"></div>
              </div>

              <div className="hero-text-slider">
                {heroTexts.map((text, index) => (
                  <div
                    key={index}
                    className={`hero-text-slide ${index === currentTextIndex ? 'active' : ''}`}
                  >
                    <h1 className="hero-title">
                      <span className="title-main">{text.title}</span>
                      <span className="title-accent">{text.subtitle}</span>
                    </h1>
                    <p className="hero-description">{text.description}</p>
                  </div>
                ))}
              </div>

              <div className="hero-actions">
                <button className="btn-premium primary" onClick={scrollToInventory}>
                  <span>Explore Collection</span>
                  <div className="btn-glow"></div>
                </button>
                <button className="btn-premium secondary">
                  <FaPlay className="play-icon" />
                  <span>Watch Story</span>
                </button>
              </div>

              <div className="hero-stats">
                <div className="stat-item">
                  <span className="stat-number">500+</span>
                  <span className="stat-label">Premium Vehicles</span>
                </div>
                <div className="stat-divider"></div>
                <div className="stat-item">
                  <span className="stat-number">15+</span>
                  <span className="stat-label">Luxury Brands</span>
                </div>
                <div className="stat-divider"></div>
                <div className="stat-item">
                  <span className="stat-number">VIP</span>
                  <span className="stat-label">Experience</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="scroll-indicator" onClick={scrollToInventory}>
          <div className="scroll-text">Discover More</div>
          <FaChevronDown className="scroll-arrow" />
        </div>
      </div>
    </section>
  )
}

export default VideoHero
