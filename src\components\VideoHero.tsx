import { useState, useEffect } from 'react'
import { FaPlay, FaChevronDown } from 'react-icons/fa'

const VideoHero = () => {
  const [isVideoLoaded, setIsVideoLoaded] = useState(false)
  const [currentTextIndex, setCurrentTextIndex] = useState(0)

  const heroTexts = [
    {
      title: "Experience the Drive",
      subtitle: "Luxury in Motion",
      description: "Discover the perfect harmony of performance, elegance, and freedom on every journey"
    },
    {
      title: "Luxury in Motion",
      subtitle: "Crafted for Excellence",
      description: "Where precision engineering meets timeless design in the world's finest automobiles"
    },
    {
      title: "Drive Beyond Limits",
      subtitle: "Premium Performance",
      description: "Experience automotive artistry that transforms every road into an extraordinary adventure"
    }
  ]

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTextIndex((prev) => (prev + 1) % heroTexts.length)
    }, 4000)
    return () => clearInterval(interval)
  }, [])

  const scrollToInventory = () => {
    const inventorySection = document.querySelector('.search-filter')
    if (inventorySection) {
      inventorySection.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <section className="video-hero">
      <div className="video-container">
        <video
          className="hero-video"
          autoPlay
          muted
          loop
          playsInline
          onLoadedData={() => setIsVideoLoaded(true)}
          onError={() => setIsVideoLoaded(true)}
          style={{ opacity: 0.3 }}
        >
          <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/mp4" />
          <source src="https://www.w3schools.com/html/mov_bbb.mp4" type="video/mp4" />
          <source src="https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4" type="video/mp4" />
        </video>

        {/* Fallback background when video fails to load */}
        <div className="hero-fallback-bg"></div>

        <div className="video-overlay"></div>

        <div className="hero-content-wrapper">
          <div className="hero-content-container">
            <div className={`hero-text-content ${isVideoLoaded ? 'loaded' : ''}`}>
              <div className="hero-badge">
                <span className="badge-text">Premium Collection</span>
                <div className="badge-glow"></div>
              </div>

              <div className="hero-text-slider">
                {heroTexts.map((text, index) => (
                  <div
                    key={index}
                    className={`hero-text-slide ${index === currentTextIndex ? 'active' : ''}`}
                  >
                    <h1 className="hero-title">
                      <span className="title-main">{text.title}</span>
                      <span className="title-accent">{text.subtitle}</span>
                    </h1>
                    <p className="hero-description">{text.description}</p>
                  </div>
                ))}
              </div>

              <div className="hero-actions">
                <button className="btn-premium primary" onClick={scrollToInventory}>
                  <span>Explore Inventory</span>
                  <div className="btn-glow"></div>
                </button>
                <button className="btn-premium secondary">
                  <FaPlay className="play-icon" />
                  <span>Book a Test Drive</span>
                </button>
              </div>

              <div className="hero-stats">
                <div className="stat-item">
                  <span className="stat-number">500+</span>
                  <span className="stat-label">Premium Vehicles</span>
                </div>
                <div className="stat-divider"></div>
                <div className="stat-item">
                  <span className="stat-number">15+</span>
                  <span className="stat-label">Luxury Brands</span>
                </div>
                <div className="stat-divider"></div>
                <div className="stat-item">
                  <span className="stat-number">VIP</span>
                  <span className="stat-label">Experience</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="scroll-indicator" onClick={scrollToInventory}>
          <div className="scroll-text">Discover More</div>
          <FaChevronDown className="scroll-arrow" />
        </div>
      </div>
    </section>
  )
}

export default VideoHero
