import { useState, useEffect } from 'react'
import { FaPlay, FaChevronDown } from 'react-icons/fa'

const VideoHero = () => {
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0)
  const [currentTextIndex, setCurrentTextIndex] = useState(0)
  const [isLoaded, setIsLoaded] = useState(false)

  const luxuryCarSlides = [
    {
      image: "https://images.unsplash.com/photo-1544636331-e26879cd4d9b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1974&q=80",
      title: "Exotic Destinations",
      subtitle: "Luxury Unleashed",
      description: "Experience the world's finest automobiles in breathtaking exotic locations"
    },
    {
      image: "https://images.unsplash.com/photo-1503376780353-7e6692767b70?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      title: "Coastal Elegance",
      subtitle: "Ocean Drive Luxury",
      description: "Where premium automotive design meets stunning coastal landscapes"
    },
    {
      image: "https://images.unsplash.com/photo-1492144534655-ae79c964c9d7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1983&q=80",
      title: "Mountain Majesty",
      subtitle: "Alpine Performance",
      description: "Discover luxury vehicles conquering the world's most scenic mountain routes"
    },
    {
      image: "https://images.unsplash.com/photo-1525609004556-c46c7d6cf023?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2052&q=80",
      title: "Urban Sophistication",
      subtitle: "City Luxury",
      description: "Premium automobiles defining elegance in the world's most exclusive cities"
    },
    {
      image: "https://images.unsplash.com/photo-1583121274602-3e2820c69888?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      title: "Desert Dreams",
      subtitle: "Endless Horizons",
      description: "Luxury vehicles exploring vast desert landscapes and infinite possibilities"
    }
  ]

  useEffect(() => {
    const slideInterval = setInterval(() => {
      setCurrentSlideIndex((prev) => (prev + 1) % luxuryCarSlides.length)
    }, 6000)

    return () => clearInterval(slideInterval)
  }, [luxuryCarSlides.length])

  useEffect(() => {
    const textInterval = setInterval(() => {
      setCurrentTextIndex((prev) => (prev + 1) % luxuryCarSlides.length)
    }, 4000)

    return () => clearInterval(textInterval)
  }, [luxuryCarSlides.length])

  useEffect(() => {
    setIsLoaded(true)
  }, [])

  const scrollToInventory = () => {
    const inventorySection = document.querySelector('.search-filter')
    if (inventorySection) {
      inventorySection.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <section className="video-hero">
      <div className="slideshow-container">
        {luxuryCarSlides.map((slide, index) => (
          <div
            key={index}
            className={`hero-slide ${index === currentSlideIndex ? 'active' : ''}`}
            style={{
              backgroundImage: `url(${slide.image})`,
            }}
          >
            <div className="slide-overlay"></div>
          </div>
        ))}



        <div className="video-overlay"></div>

        <div className="hero-content-wrapper">
          <div className="hero-content-container">
            <div className={`hero-text-content ${isLoaded ? 'loaded' : ''}`}>
              <div className="hero-badge">
                <span className="badge-text">Premium Collection</span>
                <div className="badge-glow"></div>
              </div>

              <div className="hero-text-slider">
                {luxuryCarSlides.map((slide, index) => (
                  <div
                    key={index}
                    className={`hero-text-slide ${index === currentTextIndex ? 'active' : ''}`}
                  >
                    <h1 className="hero-title">
                      <span className="title-main">{slide.title}</span>
                      <span className="title-accent">{slide.subtitle}</span>
                    </h1>
                    <p className="hero-description">{slide.description}</p>
                  </div>
                ))}
              </div>

              <div className="hero-actions">
                <button className="btn-premium primary" onClick={scrollToInventory}>
                  <span>Explore Inventory</span>
                  <div className="btn-glow"></div>
                </button>
                <button className="btn-premium secondary">
                  <FaPlay className="play-icon" />
                  <span>Book a Test Drive</span>
                </button>
              </div>

              <div className="hero-stats">
                <div className="stat-item">
                  <span className="stat-number">500+</span>
                  <span className="stat-label">Premium Vehicles</span>
                </div>
                <div className="stat-divider"></div>
                <div className="stat-item">
                  <span className="stat-number">15+</span>
                  <span className="stat-label">Luxury Brands</span>
                </div>
                <div className="stat-divider"></div>
                <div className="stat-item">
                  <span className="stat-number">VIP</span>
                  <span className="stat-label">Experience</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="scroll-indicator" onClick={scrollToInventory}>
          <div className="scroll-text">Discover More</div>
          <FaChevronDown className="scroll-arrow" />
        </div>
      </div>
    </section>
  )
}

export default VideoHero
