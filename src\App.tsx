import { useState } from 'react'
import Header from './components/Header'
import VideoHero from './components/VideoHero'
import SearchFilter from './components/SearchFilter'
import CarGrid from './components/CarGrid'
import Footer from './components/Footer'
import CarModal from './components/CarModal'
import { cars } from './data/cars.js'
import './App.css'

interface FilterOptions {
  make?: string;
  model?: string;
  type?: string;
  fuelType?: string;
  minPrice?: number;
  maxPrice?: number;
  minYear?: number;
  maxYear?: number;
}

interface Car {
  id: number;
  name: string;
  price: number;
  year: number;
  mileage: number;
  fuelType: string;
  type: string;
  make: string;
  model: string;
  image: string;
  featured: boolean;
  condition: string;
}

function App() {
  const [filteredCars, setFilteredCars] = useState(cars)
  const [selectedCar, setSelectedCar] = useState<Car | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  const handleFilter = (filters: FilterOptions) => {
    let filtered = cars

    if (filters.make) {
      filtered = filtered.filter(car => car.make === filters.make)
    }
    if (filters.model) {
      filtered = filtered.filter(car => car.model === filters.model)
    }
    if (filters.type) {
      filtered = filtered.filter(car => car.type === filters.type)
    }
    if (filters.fuelType) {
      filtered = filtered.filter(car => car.fuelType === filters.fuelType)
    }
    if (filters.minPrice) {
      filtered = filtered.filter(car => car.price >= filters.minPrice)
    }
    if (filters.maxPrice) {
      filtered = filtered.filter(car => car.price <= filters.maxPrice)
    }
    if (filters.minYear) {
      filtered = filtered.filter(car => car.year >= filters.minYear)
    }
    if (filters.maxYear) {
      filtered = filtered.filter(car => car.year <= filters.maxYear)
    }

    setFilteredCars(filtered)
  }

  const handleOpenModal = (car: Car) => {
    setSelectedCar(car)
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setSelectedCar(null)
  }

  return (
    <div className="app">
      <Header />
      <VideoHero />
      <SearchFilter onFilter={handleFilter} />
      <CarGrid cars={filteredCars} onOpenModal={handleOpenModal} />
      <Footer />

      {selectedCar && (
        <CarModal
          car={selectedCar}
          isOpen={isModalOpen}
          onClose={handleCloseModal}
        />
      )}
    </div>
  )
}

export default App
