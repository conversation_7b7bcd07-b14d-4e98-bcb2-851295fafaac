import { useState, useEffect } from 'react'
import { FaChevronLeft, FaChevronRight, FaPlay } from 'react-icons/fa'
import { cars } from '../data/cars.js'

const Hero = () => {
  const featuredCars = cars.filter(car => car.featured)
  const [currentSlide, setCurrentSlide] = useState(0)

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % featuredCars.length)
    }, 5000)

    return () => clearInterval(timer)
  }, [featuredCars.length])

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % featuredCars.length)
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + featuredCars.length) % featuredCars.length)
  }

  const currentCar = featuredCars[currentSlide]

  return (
    <section className="hero">
      <div className="hero-container">
        <div className="hero-content">
          <div className="hero-text">
            <h1 className="hero-title">
              Find Your Perfect
              <span className="hero-highlight"> Dream Car</span>
            </h1>
            <p className="hero-description">
              Discover premium vehicles with unmatched quality and service. 
              From luxury sedans to powerful SUVs, we have the perfect car waiting for you.
            </p>
            <div className="hero-buttons">
              <button className="btn btn-primary">
                <FaPlay className="btn-icon" />
                Explore Inventory
              </button>
              <button className="btn btn-secondary">Schedule Test Drive</button>
            </div>
          </div>

          <div className="hero-carousel">
            <div className="carousel-container">
              <button className="carousel-btn carousel-btn-prev" onClick={prevSlide}>
                <FaChevronLeft />
              </button>
              
              <div className="carousel-slide">
                <div className="car-showcase">
                  <img 
                    src={currentCar?.image} 
                    alt={currentCar?.name}
                    className="showcase-image"
                  />
                  <div className="showcase-overlay">
                    <h3 className="showcase-name">{currentCar?.name}</h3>
                    <p className="showcase-price">${currentCar?.price.toLocaleString()}</p>
                    <span className="showcase-badge">{currentCar?.condition}</span>
                  </div>
                </div>
              </div>

              <button className="carousel-btn carousel-btn-next" onClick={nextSlide}>
                <FaChevronRight />
              </button>
            </div>

            <div className="carousel-indicators">
              {featuredCars.map((_, index) => (
                <button
                  key={index}
                  className={`indicator ${index === currentSlide ? 'active' : ''}`}
                  onClick={() => setCurrentSlide(index)}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Hero
