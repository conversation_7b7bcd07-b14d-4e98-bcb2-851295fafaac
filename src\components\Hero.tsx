import { useState, useEffect } from 'react'
import { FaChevronLeft, FaChevronRight, FaPlay } from 'react-icons/fa'
import { cars } from '../data/cars.js'

const Hero = () => {
  const featuredCars = cars.filter(car => car.featured)
  const [currentSlide, setCurrentSlide] = useState(0)

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % featuredCars.length)
    }, 5000)

    return () => clearInterval(timer)
  }, [featuredCars.length])

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % featuredCars.length)
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + featuredCars.length) % featuredCars.length)
  }

  const currentCar = featuredCars[currentSlide]

  return (
    <section className="hero">
      <div className="hero-container">
        <div className="hero-content">
          <div className="hero-text">
            <h1 className="hero-title">
              Discover Your Perfect
              <span className="hero-highlight"> Vehicle</span>
            </h1>
            <p className="hero-description">
              Experience excellence in automotive retail with our curated selection of premium vehicles.
              From cutting-edge technology to timeless luxury, we deliver quality that exceeds expectations.
            </p>
            <div className="hero-buttons">
              <button
                className="btn btn-primary"
                onClick={() => {
                  document.getElementById('inventory')?.scrollIntoView({
                    behavior: 'smooth'
                  });
                }}
              >
                <FaPlay className="btn-icon" />
                Browse Inventory
              </button>
              <button
                className="btn btn-secondary"
                onClick={() => {
                  alert('Thank you for your interest! Please call us at (************* to schedule your consultation.');
                }}
              >
                Schedule Consultation
              </button>
            </div>
          </div>

          <div className="hero-carousel">
            <div className="carousel-container">
              <button
                className="carousel-btn carousel-btn-prev"
                onClick={prevSlide}
                aria-label="Previous car"
              >
                <FaChevronLeft />
              </button>

              <div className="carousel-slide">
                <div className="car-showcase">
                  <img
                    src={currentCar?.image}
                    alt={`${currentCar?.name} - ${currentCar?.condition} car for $${currentCar?.price.toLocaleString()}`}
                    className="showcase-image"
                  />
                  <div className="showcase-overlay">
                    <h3 className="showcase-name">{currentCar?.name}</h3>
                    <p className="showcase-price">${currentCar?.price.toLocaleString()}</p>
                    <span className="showcase-badge">{currentCar?.condition}</span>
                  </div>
                </div>
              </div>

              <button
                className="carousel-btn carousel-btn-next"
                onClick={nextSlide}
                aria-label="Next car"
              >
                <FaChevronRight />
              </button>
            </div>

            <div className="carousel-indicators" role="tablist" aria-label="Featured cars">
              {featuredCars.map((car, index) => (
                <button
                  key={index}
                  className={`indicator ${index === currentSlide ? 'active' : ''}`}
                  onClick={() => setCurrentSlide(index)}
                  aria-label={`View ${car.name}`}
                  aria-selected={index === currentSlide}
                  role="tab"
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Hero
