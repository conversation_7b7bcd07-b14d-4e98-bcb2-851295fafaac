import { FaCar, FaBars, FaTimes } from 'react-icons/fa'
import { useState } from 'react'

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  return (
    <header className="header">
      <div className="header-container">
        <div className="logo">
          <FaCar className="logo-icon" />
          <span className="logo-text">AutoElite</span>
        </div>

        <nav
          className={`nav ${isMenuOpen ? 'nav-open' : ''}`}
          aria-label="Main navigation"
        >
          <ul className="nav-list">
            <li><a href="#home" className="nav-link">Home</a></li>
            <li><a href="#inventory" className="nav-link">Inventory</a></li>
            <li><a href="#financing" className="nav-link">Financing</a></li>
            <li><a href="#contact" className="nav-link">Contact Us</a></li>
          </ul>
        </nav>

        <button
          className="menu-toggle"
          onClick={toggleMenu}
          aria-expanded={isMenuOpen}
          aria-controls="main-navigation"
          aria-label={isMenuOpen ? 'Close menu' : 'Open menu'}
        >
          {isMenuOpen ? <FaTimes /> : <FaBars />}
        </button>
      </div>
    </header>
  )
}

export default Header
