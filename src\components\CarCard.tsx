import { FaGasPump, FaTachometerAlt, FaCalendarAlt, FaHeart, FaEye } from 'react-icons/fa'
import { useState } from 'react'

const CarCard = ({ car }) => {
  const [isLiked, setIsLiked] = useState(false)

  const toggleLike = () => {
    setIsLiked(!isLiked)
  }

  return (
    <article className="car-card">
      <div className="card-image-container">
        <img
          src={car.image}
          alt={`${car.name} - ${car.year} ${car.condition} car with ${car.mileage.toLocaleString()} miles`}
          className="card-image"
        />
        <div className="card-overlay">
          <button
            className={`like-btn ${isLiked ? 'liked' : ''}`}
            onClick={toggleLike}
            aria-label={isLiked ? `Remove ${car.name} from favorites` : `Add ${car.name} to favorites`}
          >
            <FaHeart />
          </button>
          <span className={`condition-badge ${car.condition.toLowerCase()}`}>
            {car.condition}
          </span>
        </div>
        <div className="card-hover-actions">
          <button className="action-btn" aria-label={`Quick view of ${car.name}`}>
            <FaEye />
            Quick View
          </button>
        </div>
      </div>

      <div className="card-content">
        <div className="card-header">
          <h3 className="car-name">{car.name}</h3>
          <p className="car-price">${car.price.toLocaleString()}</p>
        </div>

        <div className="car-details">
          <div className="detail-item">
            <FaCalendarAlt className="detail-icon" />
            <span>{car.year}</span>
          </div>
          <div className="detail-item">
            <FaTachometerAlt className="detail-icon" />
            <span>{car.mileage.toLocaleString()} mi</span>
          </div>
          <div className="detail-item">
            <FaGasPump className="detail-icon" />
            <span>{car.fuelType}</span>
          </div>
        </div>

        <div className="card-actions">
          <button className="btn btn-outline">View Details</button>
          <button className="btn btn-primary">Buy Now</button>
        </div>
      </div>
    </div>
  )
}

export default CarCard
