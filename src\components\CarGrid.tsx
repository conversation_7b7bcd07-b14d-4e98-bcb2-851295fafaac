import CarCard from './CarCard'

const CarGrid = ({ cars }) => {
  return (
    <section className="car-grid-section">
      <div className="grid-container">
        <div className="grid-header">
          <h2 className="grid-title">Our Premium Collection</h2>
          <p className="grid-subtitle">
            Discover exceptional vehicles handpicked for quality and performance
          </p>
          <div className="results-count">
            {cars.length} {cars.length === 1 ? 'vehicle' : 'vehicles'} found
          </div>
        </div>

        {cars.length === 0 ? (
          <div className="no-results">
            <div className="no-results-content">
              <h3>No vehicles found</h3>
              <p>Try adjusting your search criteria to find more options.</p>
            </div>
          </div>
        ) : (
          <div className="car-grid">
            {cars.map((car, index) => (
              <div 
                key={car.id} 
                className={`grid-item ${index % 3 === 1 ? 'elevated' : ''}`}
                style={{
                  animationDelay: `${index * 0.1}s`
                }}
              >
                <CarCard car={car} />
              </div>
            ))}
          </div>
        )}
      </div>
    </section>
  )
}

export default CarGrid
