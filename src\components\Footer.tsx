import { FaCar, FaPhone, FaEnvelope, FaMapMarkerAlt, FaFacebook, FaTwitter, FaInstagram, FaLinkedin } from 'react-icons/fa'

const Footer = () => {
  return (
    <footer className="footer">
      <div className="footer-container">
        <div className="footer-content">
          <div className="footer-section">
            <div className="footer-logo">
              <FaCar className="footer-logo-icon" />
              <span className="footer-logo-text">AutoElite</span>
            </div>
            <p className="footer-description">
              Your trusted partner in finding the perfect vehicle. 
              We offer premium cars with exceptional service and unmatched quality.
            </p>
            <div className="social-links">
              <a href="#" className="social-link">
                <FaFacebook />
              </a>
              <a href="#" className="social-link">
                <FaTwitter />
              </a>
              <a href="#" className="social-link">
                <FaInstagram />
              </a>
              <a href="#" className="social-link">
                <FaLinkedin />
              </a>
            </div>
          </div>

          <div className="footer-section">
            <h3 className="footer-title">Quick Links</h3>
            <ul className="footer-links">
              <li><a href="#home">Home</a></li>
              <li><a href="#inventory">Inventory</a></li>
              <li><a href="#financing">Financing</a></li>
              <li><a href="#about">About Us</a></li>
              <li><a href="#contact">Contact</a></li>
            </ul>
          </div>

          <div className="footer-section">
            <h3 className="footer-title">Services</h3>
            <ul className="footer-links">
              <li><a href="#new-cars">New Cars</a></li>
              <li><a href="#used-cars">Used Cars</a></li>
              <li><a href="#trade-in">Trade-In</a></li>
              <li><a href="#service">Service Center</a></li>
              <li><a href="#parts">Parts & Accessories</a></li>
            </ul>
          </div>

          <div className="footer-section">
            <h3 className="footer-title">Contact Info</h3>
            <div className="contact-info">
              <div className="contact-item">
                <FaMapMarkerAlt className="contact-icon" />
                <span>123 Auto Street, Car City, CC 12345</span>
              </div>
              <div className="contact-item">
                <FaPhone className="contact-icon" />
                <span>(*************</span>
              </div>
              <div className="contact-item">
                <FaEnvelope className="contact-icon" />
                <span><EMAIL></span>
              </div>
            </div>
            
            <div className="business-hours">
              <h4>Business Hours</h4>
              <p>Mon - Fri: 9:00 AM - 8:00 PM</p>
              <p>Sat: 9:00 AM - 6:00 PM</p>
              <p>Sun: 12:00 PM - 5:00 PM</p>
            </div>
          </div>
        </div>

        <div className="footer-bottom">
          <div className="footer-bottom-content">
            <p>&copy; 2024 AutoElite. All rights reserved.</p>
            <div className="footer-bottom-links">
              <a href="#privacy">Privacy Policy</a>
              <a href="#terms">Terms of Service</a>
              <a href="#sitemap">Sitemap</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
