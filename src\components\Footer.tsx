import { FaCar, FaPhone, FaEnvelope, FaMapMarkerAlt, FaFacebook, FaTwitter, FaInstagram, FaLinkedin } from 'react-icons/fa'

const Footer = () => {
  return (
    <footer className="footer" id="contact">
      <div className="footer-container">
        <div className="footer-content">
          <div className="footer-section">
            <div className="footer-logo">
              <FaCar className="footer-logo-icon" />
              <span className="footer-logo-text">AutoElite</span>
            </div>
            <p className="footer-description">
              Your trusted partner in finding the perfect vehicle.
              We offer premium cars with exceptional service and unmatched quality.
            </p>
            <div className="social-links">
              <a
                href="#"
                className="social-link"
                onClick={(e) => {
                  e.preventDefault();
                  alert('Follow us on Facebook: @AutoEliteDealer\n\nStay updated with our latest inventory and special offers!');
                }}
                aria-label="Follow us on Facebook"
              >
                <FaFacebook />
              </a>
              <a
                href="#"
                className="social-link"
                onClick={(e) => {
                  e.preventDefault();
                  alert('Follow us on Twitter: @AutoEliteDealer\n\nGet real-time updates on new arrivals and deals!');
                }}
                aria-label="Follow us on Twitter"
              >
                <FaTwitter />
              </a>
              <a
                href="#"
                className="social-link"
                onClick={(e) => {
                  e.preventDefault();
                  alert('Follow us on Instagram: @AutoEliteDealer\n\nSee stunning photos of our premium vehicles!');
                }}
                aria-label="Follow us on Instagram"
              >
                <FaInstagram />
              </a>
              <a
                href="#"
                className="social-link"
                onClick={(e) => {
                  e.preventDefault();
                  alert('Connect with us on LinkedIn: AutoElite Dealership\n\nProfessional automotive services and business partnerships!');
                }}
                aria-label="Connect with us on LinkedIn"
              >
                <FaLinkedin />
              </a>
            </div>
          </div>

          <div className="footer-section">
            <h3 className="footer-title">Quick Links</h3>
            <ul className="footer-links">
              <li>
                <a
                  href="#home"
                  onClick={(e) => {
                    e.preventDefault();
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                  }}
                >
                  Home
                </a>
              </li>
              <li>
                <a
                  href="#inventory"
                  onClick={(e) => {
                    e.preventDefault();
                    document.getElementById('inventory')?.scrollIntoView({
                      behavior: 'smooth'
                    });
                  }}
                >
                  Inventory
                </a>
              </li>
              <li>
                <a
                  href="#financing"
                  onClick={(e) => {
                    e.preventDefault();
                    alert('Financing Options:\n\n• Competitive rates from 2.9% APR\n• Flexible terms up to 84 months\n• Trade-in evaluations\n• Online pre-approval\n• Bad credit? No problem!\n\nContact us for personalized solutions!');
                  }}
                >
                  Financing
                </a>
              </li>
              <li>
                <a
                  href="#about"
                  onClick={(e) => {
                    e.preventDefault();
                    alert('About AutoElite:\n\n• Family-owned since 1985\n• Over 500 vehicles in stock\n• Award-winning customer service\n• Certified pre-owned vehicles\n• Full-service department\n• 30+ years of automotive excellence\n\nYour satisfaction is our priority!');
                  }}
                >
                  About Us
                </a>
              </li>
              <li>
                <a
                  href="#contact"
                  onClick={(e) => {
                    e.preventDefault();
                    document.getElementById('contact')?.scrollIntoView({
                      behavior: 'smooth'
                    });
                  }}
                >
                  Contact
                </a>
              </li>
            </ul>
          </div>

          <div className="footer-section">
            <h3 className="footer-title">Services</h3>
            <ul className="footer-links">
              <li>
                <a
                  href="#new-cars"
                  onClick={(e) => {
                    e.preventDefault();
                    alert('New Cars:\n\n• Latest 2024 models\n• Factory warranties\n• Special financing offers\n• Trade-in programs\n• Delivery available\n\nBrowse our new car inventory below!');
                    document.getElementById('inventory')?.scrollIntoView({
                      behavior: 'smooth'
                    });
                  }}
                >
                  New Cars
                </a>
              </li>
              <li>
                <a
                  href="#used-cars"
                  onClick={(e) => {
                    e.preventDefault();
                    alert('Used Cars:\n\n• Certified pre-owned vehicles\n• Multi-point inspections\n• Extended warranties available\n• CarFax reports included\n• Best value pricing\n\nCheck out our used car selection!');
                    document.getElementById('inventory')?.scrollIntoView({
                      behavior: 'smooth'
                    });
                  }}
                >
                  Used Cars
                </a>
              </li>
              <li>
                <a
                  href="#trade-in"
                  onClick={(e) => {
                    e.preventDefault();
                    alert('Trade-In Services:\n\n• Free vehicle appraisals\n• Competitive trade values\n• Same-day evaluations\n• Apply trade value to purchase\n• No obligation quotes\n\nCall (************* to schedule your trade-in evaluation!');
                  }}
                >
                  Trade-In
                </a>
              </li>
              <li>
                <a
                  href="#service"
                  onClick={(e) => {
                    e.preventDefault();
                    alert('Service Center:\n\n• Factory-trained technicians\n• Genuine OEM parts\n• Oil changes & maintenance\n• Warranty repairs\n• Loaner vehicles available\n• Service while you wait\n\nSchedule your service appointment today!');
                  }}
                >
                  Service Center
                </a>
              </li>
              <li>
                <a
                  href="#parts"
                  onClick={(e) => {
                    e.preventDefault();
                    alert('Parts & Accessories:\n\n• Genuine OEM parts\n• Aftermarket accessories\n• Performance upgrades\n• Installation services\n• Special order items\n• Competitive pricing\n\nContact our parts department for availability!');
                  }}
                >
                  Parts & Accessories
                </a>
              </li>
            </ul>
          </div>

          <div className="footer-section">
            <h3 className="footer-title">Contact Info</h3>
            <div className="contact-info">
              <div
                className="contact-item"
                style={{ cursor: 'pointer' }}
                onClick={() => {
                  alert('Visit our showroom:\n\n123 Auto Street\nCar City, CC 12345\n\nOpen 7 days a week!\nFree parking available.');
                }}
              >
                <FaMapMarkerAlt className="contact-icon" />
                <span>123 Auto Street, Car City, CC 12345</span>
              </div>
              <div
                className="contact-item"
                style={{ cursor: 'pointer' }}
                onClick={() => {
                  if (confirm('Call AutoElite at (*************?')) {
                    window.open('tel:+15551234567');
                  }
                }}
              >
                <FaPhone className="contact-icon" />
                <span>(*************</span>
              </div>
              <div
                className="contact-item"
                style={{ cursor: 'pointer' }}
                onClick={() => {
                  if (confirm('Send <NAME_EMAIL>?')) {
                    window.open('mailto:<EMAIL>?subject=Inquiry about vehicles&body=Hello, I am interested in learning more about your vehicles.');
                  }
                }}
              >
                <FaEnvelope className="contact-icon" />
                <span><EMAIL></span>
              </div>
            </div>

            <div className="business-hours">
              <h4>Business Hours</h4>
              <p>Mon - Fri: 9:00 AM - 8:00 PM</p>
              <p>Sat: 9:00 AM - 6:00 PM</p>
              <p>Sun: 12:00 PM - 5:00 PM</p>
            </div>
          </div>
        </div>

        <div className="footer-bottom">
          <div className="footer-bottom-content">
            <p>&copy; 2024 AutoElite. All rights reserved.</p>
            <div className="footer-bottom-links">
              <a href="#privacy">Privacy Policy</a>
              <a href="#terms">Terms of Service</a>
              <a href="#sitemap">Sitemap</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
