export const cars = [
  {
    id: 1,
    name: "BMW X5",
    price: 65000,
    year: 2023,
    mileage: 12000,
    fuelType: "Gasoline",
    type: "SUV",
    make: "BMW",
    model: "X5",
    image: "https://images.unsplash.com/photo-1555215695-3004980ad54e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    featured: true,
    condition: "Used"
  },
  {
    id: 2,
    name: "Mercedes-Benz C-Class",
    price: 45000,
    year: 2024,
    mileage: 0,
    fuelType: "Gasoline",
    type: "Sedan",
    make: "Mercedes-Benz",
    model: "C-Class",
    image: "https://images.unsplash.com/photo-1618843479313-40f8afb4b4d8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    featured: true,
    condition: "New"
  },
  {
    id: 3,
    name: "Audi A4",
    price: 38000,
    year: 2023,
    mileage: 8500,
    fuelType: "Gasoline",
    type: "Sedan",
    make: "Audi",
    model: "A4",
    image: "https://images.unsplash.com/photo-1606664515524-ed2f786a0bd6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    featured: false,
    condition: "Used"
  },
  {
    id: 4,
    name: "Tesla Model 3",
    price: 42000,
    year: 2024,
    mileage: 0,
    fuelType: "Electric",
    type: "Sedan",
    make: "Tesla",
    model: "Model 3",
    image: "https://images.unsplash.com/photo-1560958089-b8a1929cea89?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80",
    featured: true,
    condition: "New"
  },
  {
    id: 5,
    name: "Ford F-150",
    price: 55000,
    year: 2023,
    mileage: 15000,
    fuelType: "Gasoline",
    type: "Truck",
    make: "Ford",
    model: "F-150",
    image: "https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    featured: false,
    condition: "Used"
  },
  {
    id: 6,
    name: "Honda Civic",
    price: 28000,
    year: 2024,
    mileage: 0,
    fuelType: "Gasoline",
    type: "Sedan",
    make: "Honda",
    model: "Civic",
    image: "https://images.unsplash.com/photo-1606664515524-ed2f786a0bd6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    featured: false,
    condition: "New"
  },
  {
    id: 7,
    name: "Porsche 911",
    price: 120000,
    year: 2023,
    mileage: 5000,
    fuelType: "Gasoline",
    type: "Sports Car",
    make: "Porsche",
    model: "911",
    image: "https://images.unsplash.com/photo-1544636331-e26879cd4d9b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2074&q=80",
    featured: true,
    condition: "Used"
  },
  {
    id: 8,
    name: "Jeep Wrangler",
    price: 48000,
    year: 2024,
    mileage: 0,
    fuelType: "Gasoline",
    type: "SUV",
    make: "Jeep",
    model: "Wrangler",
    image: "https://images.unsplash.com/photo-1606664515524-ed2f786a0bd6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
    featured: false,
    condition: "New"
  }
];

export const makes = [...new Set(cars.map(car => car.make))];
export const models = [...new Set(cars.map(car => car.model))];
export const types = [...new Set(cars.map(car => car.type))];
export const fuelTypes = [...new Set(cars.map(car => car.fuelType))];
