import { useState } from 'react'
import { FaSearch, FaFilter, FaTimes } from 'react-icons/fa'
import { makes, models, types, fuelTypes } from '../data/cars.js'

interface FilterOptions {
  make?: string;
  model?: string;
  type?: string;
  fuelType?: string;
  minPrice?: string;
  maxPrice?: string;
  minYear?: string;
  maxYear?: string;
}

interface SearchFilterProps {
  onFilter: (filters: FilterOptions) => void;
}

const SearchFilter = ({ onFilter }: SearchFilterProps) => {
  const [isFilterOpen, setIsFilterOpen] = useState(false)
  const [filters, setFilters] = useState({
    make: '',
    model: '',
    type: '',
    fuelType: '',
    minPrice: '',
    maxPrice: '',
    minYear: '',
    maxYear: ''
  })

  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value }
    setFilters(newFilters)
    onFilter(newFilters)
  }

  const clearFilters = () => {
    const clearedFilters = {
      make: '',
      model: '',
      type: '',
      fuelType: '',
      minPrice: '',
      maxPrice: '',
      minYear: '',
      maxYear: ''
    }
    setFilters(clearedFilters)
    onFilter(clearedFilters)
  }

  const toggleFilter = () => {
    setIsFilterOpen(!isFilterOpen)
  }

  return (
    <section className="search-filter">
      <div className="search-container">
        <div className="search-header">
          <h2 className="search-title">Find Your Vehicle:</h2>

          <div className="quick-search">
            <div className="search-group">
              <label>Make:</label>
              <select
                value={filters.make}
                onChange={(e) => handleFilterChange('make', e.target.value)}
                className="search-select"
              >
                <option value="">All Makes</option>
                {makes.map(make => (
                  <option key={make} value={make}>{make}</option>
                ))}
              </select>
            </div>

            <div className="search-group">
              <label>Type:</label>
              <select
                value={filters.type}
                onChange={(e) => handleFilterChange('type', e.target.value)}
                className="search-select"
              >
                <option value="">All Types</option>
                {types.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>

            <div className="search-group">
              <label>Max Price:</label>
              <input
                type="number"
                placeholder="Any Price"
                value={filters.maxPrice}
                onChange={(e) => handleFilterChange('maxPrice', e.target.value)}
                className="search-input"
              />
            </div>

            <button
              className="search-btn"
              onClick={() => {
                // Apply current quick search filters
                const quickFilters = {
                  make: filters.make,
                  type: filters.type,
                  maxPrice: filters.maxPrice ? parseInt(filters.maxPrice) : undefined
                };
                onFilter(quickFilters);

                // Scroll to results
                document.getElementById('inventory')?.scrollIntoView({
                  behavior: 'smooth'
                });
              }}
            >
              <FaSearch />
              Search
            </button>

            <button
              className="filter-toggle"
              onClick={toggleFilter}
              aria-expanded={isFilterOpen}
              aria-controls="advanced-filters"
            >
              <FaFilter className="filter-icon" />
              More Filters
            </button>
          </div>
        </div>

        <div
          className={`advanced-filters ${isFilterOpen ? 'filters-open' : ''}`}
          id="advanced-filters"
          aria-hidden={!isFilterOpen}
        >
          <div className="filters-header">
            <h3>Advanced Filters</h3>
            <button
              className="close-filters"
              onClick={toggleFilter}
              aria-label="Close advanced filters"
            >
              <FaTimes />
            </button>
          </div>

          <div className="filters-grid">
            <div className="filter-group">
              <label>Model</label>
              <select
                value={filters.model}
                onChange={(e) => handleFilterChange('model', e.target.value)}
                className="filter-select"
              >
                <option value="">All Models</option>
                {models.map(model => (
                  <option key={model} value={model}>{model}</option>
                ))}
              </select>
            </div>

            <div className="filter-group">
              <label>Fuel Type</label>
              <select
                value={filters.fuelType}
                onChange={(e) => handleFilterChange('fuelType', e.target.value)}
                className="filter-select"
              >
                <option value="">All Fuel Types</option>
                {fuelTypes.map(fuel => (
                  <option key={fuel} value={fuel}>{fuel}</option>
                ))}
              </select>
            </div>

            <div className="filter-group">
              <label>Min Price</label>
              <input
                type="number"
                placeholder="$0"
                value={filters.minPrice}
                onChange={(e) => handleFilterChange('minPrice', e.target.value)}
                className="filter-input"
              />
            </div>

            <div className="filter-group">
              <label>Max Price</label>
              <input
                type="number"
                placeholder="$999,999"
                value={filters.maxPrice}
                onChange={(e) => handleFilterChange('maxPrice', e.target.value)}
                className="filter-input"
              />
            </div>

            <div className="filter-group">
              <label>Min Year</label>
              <input
                type="number"
                placeholder="2020"
                value={filters.minYear}
                onChange={(e) => handleFilterChange('minYear', e.target.value)}
                className="filter-input"
              />
            </div>

            <div className="filter-group">
              <label>Max Year</label>
              <input
                type="number"
                placeholder="2024"
                value={filters.maxYear}
                onChange={(e) => handleFilterChange('maxYear', e.target.value)}
                className="filter-input"
              />
            </div>
          </div>

          <div className="filters-actions">
            <button className="btn btn-secondary" onClick={clearFilters}>
              Clear All
            </button>
            <button className="btn btn-primary" onClick={toggleFilter}>
              Apply Filters
            </button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default SearchFilter
