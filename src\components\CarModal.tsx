import { FaTimes, FaGasPump, FaTachometerAlt, FaCalendarAlt, FaCar, FaPhone, FaEnvelope, FaHeart, FaShare } from 'react-icons/fa'
import { useState } from 'react'

interface Car {
  id: number;
  name: string;
  price: number;
  year: number;
  mileage: number;
  fuelType: string;
  type: string;
  make: string;
  model: string;
  image: string;
  featured: boolean;
  condition: string;
}

interface CarModalProps {
  car: Car;
  isOpen: boolean;
  onClose: () => void;
}

const CarModal = ({ car, isOpen, onClose }: CarModalProps) => {
  const [isLiked, setIsLiked] = useState(false)

  if (!isOpen) return null

  const toggleLike = () => {
    setIsLiked(!isLiked)
  }

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose()
    }
  }

  const handleBuyNow = () => {
    const confirmed = confirm(`Start purchase process for ${car.name}?\n\nPrice: $${car.price.toLocaleString()}\n\nThis will connect you with our sales team.`);
    if (confirmed) {
      alert(`Thank you for your interest in the ${car.name}!\n\nOur sales team will contact you shortly to complete your purchase.\n\nReference ID: ${car.id}\nPhone: (*************\nEmail: <EMAIL>`);
      onClose();
    }
  }

  const handleScheduleTest = () => {
    alert(`Schedule Test Drive for ${car.name}\n\nWe'll contact you within 24 hours to arrange your test drive.\n\nCall us directly at (************* for immediate scheduling.`);
    onClose();
  }

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: `${car.name} - AutoElite`,
        text: `Check out this ${car.year} ${car.name} for $${car.price.toLocaleString()}`,
        url: window.location.href
      });
    } else {
      alert(`Share this vehicle:\n\n${car.year} ${car.name}\nPrice: $${car.price.toLocaleString()}\nMileage: ${car.mileage.toLocaleString()} miles\n\nContact AutoElite at (*************`);
    }
  }

  return (
    <div className="modal-backdrop" onClick={handleBackdropClick}>
      <div className="modal-content">
        <div className="modal-header">
          <div className="modal-title-section">
            <h2 className="modal-title">{car.name}</h2>
            <span className={`modal-condition-badge ${car.condition.toLowerCase()}`}>
              {car.condition}
            </span>
          </div>
          <div className="modal-actions">
            <button 
              className={`modal-action-btn ${isLiked ? 'liked' : ''}`}
              onClick={toggleLike}
              aria-label={isLiked ? 'Remove from favorites' : 'Add to favorites'}
            >
              <FaHeart />
            </button>
            <button 
              className="modal-action-btn"
              onClick={handleShare}
              aria-label="Share this vehicle"
            >
              <FaShare />
            </button>
            <button 
              className="modal-close-btn"
              onClick={onClose}
              aria-label="Close modal"
            >
              <FaTimes />
            </button>
          </div>
        </div>

        <div className="modal-body">
          <div className="modal-image-section">
            <img 
              src={car.image} 
              alt={`${car.name} - ${car.condition} car`}
              className="modal-image"
            />
            <div className="modal-price">
              ${car.price.toLocaleString()}
            </div>
          </div>

          <div className="modal-details-section">
            <div className="modal-specs">
              <h3>Vehicle Specifications</h3>
              <div className="specs-grid">
                <div className="spec-item">
                  <FaCalendarAlt className="spec-icon" />
                  <div className="spec-content">
                    <span className="spec-label">Year</span>
                    <span className="spec-value">{car.year}</span>
                  </div>
                </div>
                <div className="spec-item">
                  <FaTachometerAlt className="spec-icon" />
                  <div className="spec-content">
                    <span className="spec-label">Mileage</span>
                    <span className="spec-value">{car.mileage.toLocaleString()} mi</span>
                  </div>
                </div>
                <div className="spec-item">
                  <FaGasPump className="spec-icon" />
                  <div className="spec-content">
                    <span className="spec-label">Fuel Type</span>
                    <span className="spec-value">{car.fuelType}</span>
                  </div>
                </div>
                <div className="spec-item">
                  <FaCar className="spec-icon" />
                  <div className="spec-content">
                    <span className="spec-label">Type</span>
                    <span className="spec-value">{car.type}</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="modal-features">
              <h3>Key Features</h3>
              <ul className="features-list">
                <li>Premium interior with leather seating</li>
                <li>Advanced safety features</li>
                <li>Bluetooth connectivity</li>
                <li>Backup camera</li>
                <li>Climate control</li>
                <li>Alloy wheels</li>
              </ul>
            </div>

            <div className="modal-contact">
              <h3>Contact Information</h3>
              <div className="contact-options">
                <button 
                  className="contact-btn phone-btn"
                  onClick={() => {
                    if (confirm('Call AutoElite at (*************?')) {
                      window.open('tel:+15551234567');
                    }
                  }}
                >
                  <FaPhone />
                  Call (*************
                </button>
                <button 
                  className="contact-btn email-btn"
                  onClick={() => {
                    window.open(`mailto:<EMAIL>?subject=Inquiry about ${car.name}&body=Hello, I am interested in the ${car.year} ${car.name} (ID: ${car.id}) listed for $${car.price.toLocaleString()}. Please contact me with more information.`);
                  }}
                >
                  <FaEnvelope />
                  Email Us
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="modal-footer">
          <button className="btn btn-secondary" onClick={handleScheduleTest}>
            Schedule Test Drive
          </button>
          <button className="btn btn-primary" onClick={handleBuyNow}>
            Buy Now - ${car.price.toLocaleString()}
          </button>
        </div>
      </div>
    </div>
  )
}

export default CarModal
