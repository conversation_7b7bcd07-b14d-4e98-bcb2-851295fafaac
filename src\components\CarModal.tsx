import { FaTimes, FaGasPump, FaTachometerAlt, FaCalendarAlt, FaCar, FaPhone, FaEnvelope, FaHeart, FaShare } from 'react-icons/fa'
import { useState } from 'react'

interface Car {
  id: number;
  name: string;
  price: number;
  year: number;
  mileage: number;
  fuelType: string;
  type: string;
  make: string;
  model: string;
  image: string;
  featured: boolean;
  condition: string;
}

interface CarModalProps {
  car: Car;
  isOpen: boolean;
  onClose: () => void;
}

const CarModal = ({ car, isOpen, onClose }: CarModalProps) => {
  const [isLiked, setIsLiked] = useState(false)

  if (!isOpen) return null

  const toggleLike = () => {
    setIsLiked(!isLiked)
  }

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose()
    }
  }

  const handleBuyNow = () => {
    const confirmed = confirm(`Start purchase process for ${car.name}?\n\nPrice: $${car.price.toLocaleString()}\n\nThis will connect you with our sales team.`);
    if (confirmed) {
      alert(`Thank you for your interest in the ${car.name}!\n\nOur sales team will contact you shortly to complete your purchase.\n\nReference ID: ${car.id}\nPhone: (*************\nEmail: <EMAIL>`);
      onClose();
    }
  }

  const handleScheduleTest = () => {
    alert(`Schedule Test Drive for ${car.name}\n\nWe'll contact you within 24 hours to arrange your test drive.\n\nCall us directly at (************* for immediate scheduling.`);
    onClose();
  }

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: `${car.name} - AutoElite`,
        text: `Check out this ${car.year} ${car.name} for $${car.price.toLocaleString()}`,
        url: window.location.href
      });
    } else {
      alert(`Share this vehicle:\n\n${car.year} ${car.name}\nPrice: $${car.price.toLocaleString()}\nMileage: ${car.mileage.toLocaleString()} miles\n\nContact AutoElite at (*************`);
    }
  }

  return (
    <div className="modal-backdrop" onClick={handleBackdropClick}>
      <div className="modal-car-card">
        <div className="modal-card-image-container">
          <img
            src={car.image}
            alt={`${car.name} - ${car.condition} car with ${car.mileage.toLocaleString()} miles`}
            className="modal-card-image"
          />
          <div className="modal-card-overlay">
            <button
              className={`modal-like-btn ${isLiked ? 'liked' : ''}`}
              onClick={toggleLike}
              aria-label={isLiked ? `Remove ${car.name} from favorites` : `Add ${car.name} to favorites`}
            >
              <FaHeart />
            </button>
            <span className={`modal-condition-badge ${car.condition.toLowerCase()}`}>
              {car.condition}
            </span>
          </div>
          <div className="modal-card-hover-actions">
            <button
              className="modal-action-btn"
              onClick={handleShare}
              aria-label="Share this vehicle"
            >
              <FaShare />
              Share
            </button>
          </div>
          <button
            className="modal-close-btn"
            onClick={onClose}
            aria-label="Close modal"
          >
            <FaTimes />
          </button>
        </div>

        <div className="modal-card-content">
          <div className="modal-card-header">
            <h3 className="modal-car-name">{car.name}</h3>
            <p className="modal-car-price">${car.price.toLocaleString()}</p>
          </div>

          <div className="modal-car-details">
            <div className="modal-detail-item">
              <FaCalendarAlt className="modal-detail-icon" />
              <span>{car.year}</span>
            </div>
            <div className="modal-detail-item">
              <FaTachometerAlt className="modal-detail-icon" />
              <span>{car.mileage.toLocaleString()} mi</span>
            </div>
            <div className="modal-detail-item">
              <FaGasPump className="modal-detail-icon" />
              <span>{car.fuelType}</span>
            </div>
          </div>

          <div className="modal-additional-info">
            <div className="modal-info-item">
              <span className="modal-info-label">Make:</span>
              <span className="modal-info-value">{car.make}</span>
            </div>
            <div className="modal-info-item">
              <span className="modal-info-label">Model:</span>
              <span className="modal-info-value">{car.model}</span>
            </div>
            <div className="modal-info-item">
              <span className="modal-info-label">Type:</span>
              <span className="modal-info-value">{car.type}</span>
            </div>
            <div className="modal-info-item">
              <span className="modal-info-label">Vehicle ID:</span>
              <span className="modal-info-value">#{car.id}</span>
            </div>
          </div>

          <div className="modal-card-actions">
            <button
              className="btn btn-outline"
              onClick={() => {
                alert(`Contact us about the ${car.name}:\n\nPhone: (*************\nEmail: <EMAIL>\n\nReference ID: ${car.id}`);
              }}
            >
              Contact Us
            </button>
            <button
              className="btn btn-primary"
              onClick={handleBuyNow}
            >
              Buy Now
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CarModal
