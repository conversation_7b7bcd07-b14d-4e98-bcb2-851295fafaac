import { FaTimes, FaGasPump, FaTachometerAlt, FaCalendarAlt, FaCar, FaPhone, FaEnvelope, FaHeart, FaShare } from 'react-icons/fa'
import { useState } from 'react'

interface Car {
  id: number;
  name: string;
  price: number;
  year: number;
  mileage: number;
  fuelType: string;
  type: string;
  make: string;
  model: string;
  image: string;
  featured: boolean;
  condition: string;
}

interface CarModalProps {
  car: Car;
  isOpen: boolean;
  onClose: () => void;
}

const CarModal = ({ car, isOpen, onClose }: CarModalProps) => {
  const [isLiked, setIsLiked] = useState(false)

  if (!isOpen) return null

  const toggleLike = () => {
    setIsLiked(!isLiked)
  }

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose()
    }
  }

  const handleBuyNow = () => {
    const confirmed = confirm(`Start purchase process for ${car.name}?\n\nPrice: $${car.price.toLocaleString()}\n\nThis will connect you with our sales team.`);
    if (confirmed) {
      alert(`Thank you for your interest in the ${car.name}!\n\nOur sales team will contact you shortly to complete your purchase.\n\nReference ID: ${car.id}\nPhone: (*************\nEmail: <EMAIL>`);
      onClose();
    }
  }

  const handleScheduleTest = () => {
    alert(`Schedule Test Drive for ${car.name}\n\nWe'll contact you within 24 hours to arrange your test drive.\n\nCall us directly at (************* for immediate scheduling.`);
    onClose();
  }

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: `${car.name} - AutoElite`,
        text: `Check out this ${car.year} ${car.name} for $${car.price.toLocaleString()}`,
        url: window.location.href
      });
    } else {
      alert(`Share this vehicle:\n\n${car.year} ${car.name}\nPrice: $${car.price.toLocaleString()}\nMileage: ${car.mileage.toLocaleString()} miles\n\nContact AutoElite at (*************`);
    }
  }

  return (
    <div className="modal-backdrop" onClick={handleBackdropClick}>
      <div className="modal-car-card">
        <div className="modal-card-image-container">
          <img
            src={car.image}
            alt={`${car.name} - ${car.condition} car with ${car.mileage.toLocaleString()} miles`}
            className="modal-card-image"
          />
          <div className="modal-card-overlay">
            <button
              className={`modal-like-btn ${isLiked ? 'liked' : ''}`}
              onClick={toggleLike}
              aria-label={isLiked ? `Remove ${car.name} from favorites` : `Add ${car.name} to favorites`}
            >
              <FaHeart />
            </button>
            <span className={`modal-condition-badge ${car.condition.toLowerCase()}`}>
              {car.condition}
            </span>
          </div>
          <div className="modal-card-hover-actions">
            <button
              className="modal-action-btn"
              onClick={handleShare}
              aria-label="Share this vehicle"
            >
              <FaShare />
              Share
            </button>
          </div>
          <button
            className="modal-close-btn"
            onClick={onClose}
            aria-label="Close modal"
          >
            <FaTimes />
          </button>
        </div>

        <div className="modal-card-content">
          <div className="modal-card-header">
            <h3 className="modal-car-name">{car.name}</h3>
            <p className="modal-car-price">${car.price.toLocaleString()}</p>
          </div>

          <div className="modal-car-details">
            <div className="modal-detail-item">
              <FaCalendarAlt className="modal-detail-icon" />
              <span>{car.year}</span>
            </div>
            <div className="modal-detail-item">
              <FaTachometerAlt className="modal-detail-icon" />
              <span>{car.mileage.toLocaleString()} mi</span>
            </div>
            <div className="modal-detail-item">
              <FaGasPump className="modal-detail-icon" />
              <span>{car.fuelType}</span>
            </div>
          </div>

          <div className="modal-additional-info">
            <h4 className="modal-section-title">Vehicle Information</h4>
            <div className="modal-info-grid">
              <div className="modal-info-item">
                <span className="modal-info-label">Make:</span>
                <span className="modal-info-value">{car.make}</span>
              </div>
              <div className="modal-info-item">
                <span className="modal-info-label">Model:</span>
                <span className="modal-info-value">{car.model}</span>
              </div>
              <div className="modal-info-item">
                <span className="modal-info-label">Type:</span>
                <span className="modal-info-value">{car.type}</span>
              </div>
              <div className="modal-info-item">
                <span className="modal-info-label">Vehicle ID:</span>
                <span className="modal-info-value">#{car.id}</span>
              </div>
              <div className="modal-info-item">
                <span className="modal-info-label">Condition:</span>
                <span className="modal-info-value">{car.condition}</span>
              </div>
              <div className="modal-info-item">
                <span className="modal-info-label">Status:</span>
                <span className="modal-info-value">Available</span>
              </div>
            </div>
          </div>

          <div className="modal-features-section">
            <h4 className="modal-section-title">Key Features & Equipment</h4>
            <div className="modal-features-grid">
              <div className="feature-category">
                <h5>Interior</h5>
                <ul>
                  <li>Premium leather seating</li>
                  <li>Heated & ventilated seats</li>
                  <li>Dual-zone climate control</li>
                  <li>Premium sound system</li>
                  <li>Navigation system</li>
                </ul>
              </div>
              <div className="feature-category">
                <h5>Safety</h5>
                <ul>
                  <li>Advanced airbag system</li>
                  <li>Anti-lock braking (ABS)</li>
                  <li>Electronic stability control</li>
                  <li>Backup camera</li>
                  <li>Blind spot monitoring</li>
                </ul>
              </div>
              <div className="feature-category">
                <h5>Technology</h5>
                <ul>
                  <li>Bluetooth connectivity</li>
                  <li>Apple CarPlay/Android Auto</li>
                  <li>USB charging ports</li>
                  <li>Wireless charging pad</li>
                  <li>Premium infotainment</li>
                </ul>
              </div>
              <div className="feature-category">
                <h5>Exterior</h5>
                <ul>
                  <li>Alloy wheels</li>
                  <li>LED headlights</li>
                  <li>Power sunroof</li>
                  <li>Fog lights</li>
                  <li>Chrome accents</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="modal-warranty-section">
            <h4 className="modal-section-title">Warranty & Service</h4>
            <div className="warranty-info">
              <div className="warranty-item">
                <strong>Factory Warranty:</strong> {car.condition === 'New' ? 'Full manufacturer warranty included' : 'Remaining factory warranty transfers'}
              </div>
              <div className="warranty-item">
                <strong>Extended Warranty:</strong> Available for purchase
              </div>
              <div className="warranty-item">
                <strong>Service History:</strong> {car.condition === 'New' ? 'Brand new vehicle' : 'Complete service records available'}
              </div>
              <div className="warranty-item">
                <strong>Inspection:</strong> {car.condition === 'New' ? 'Factory quality control' : 'Certified pre-owned inspection completed'}
              </div>
            </div>
          </div>

          <div className="modal-financing-section">
            <h4 className="modal-section-title">Financing Options</h4>
            <div className="financing-options">
              <div className="financing-option">
                <strong>Starting APR:</strong> 2.9% (with approved credit)
              </div>
              <div className="financing-option">
                <strong>Loan Terms:</strong> 24-84 months available
              </div>
              <div className="financing-option">
                <strong>Down Payment:</strong> As low as $0 down
              </div>
              <div className="financing-option">
                <strong>Trade-In:</strong> We accept all trade-ins
              </div>
            </div>
          </div>

          <div className="modal-contact-section">
            <h4 className="modal-section-title">Contact Information</h4>
            <div className="contact-details">
              <div className="contact-method">
                <FaPhone className="contact-icon" />
                <div className="contact-info">
                  <strong>Call Us:</strong>
                  <span>(*************</span>
                  <button
                    className="contact-link"
                    onClick={() => {
                      if (confirm('Call AutoElite at (*************?')) {
                        window.open('tel:+15551234567');
                      }
                    }}
                  >
                    Call Now
                  </button>
                </div>
              </div>
              <div className="contact-method">
                <FaEnvelope className="contact-icon" />
                <div className="contact-info">
                  <strong>Email Us:</strong>
                  <span><EMAIL></span>
                  <button
                    className="contact-link"
                    onClick={() => {
                      window.open(`mailto:<EMAIL>?subject=Inquiry about ${car.name}&body=Hello, I am interested in the ${car.year} ${car.name} (ID: ${car.id}) listed for $${car.price.toLocaleString()}. Please contact me with more information.`);
                    }}
                  >
                    Send Email
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div className="modal-card-actions">
            <button
              className="btn btn-outline"
              onClick={handleScheduleTest}
            >
              Schedule Test Drive
            </button>
            <button
              className="btn btn-primary"
              onClick={handleBuyNow}
            >
              Buy Now - ${car.price.toLocaleString()}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CarModal
